# MaxDiff分析 - 使用贝叶斯方法和现代R包
# 设置CRAN镜像
options(repos = c(CRAN = "https://cran.rstudio.com/"))

# 安装和加载必要的包
install_if_missing <- function(pkg) {
  if (!require(pkg, character.only = TRUE)) {
    cat("安装包:", pkg, "\n")
    install.packages(pkg, dependencies = TRUE)
    library(pkg, character.only = TRUE)
  }
}

# 安装必要的包 - 使用可用的替代包
packages <- c("bayesm", "MCMCpack", "ggplot2", "dplyr", "reshape2", "mlogit", "MASS")
for (pkg in packages) {
  install_if_missing(pkg)
}

# 设置随机种子
set.seed(123)

# 构造MaxDiff样例数据
cat("=== 构造MaxDiff样例数据 ===\n")

# 创建属性列表（例如：智能手机品牌偏好研究）
attributes <- c("Apple", "Samsung", "Huawei", "Xiaomi", "OnePlus", "Google", "Sony", "LG")
n_attributes <- length(attributes)

# 实验设计参数
n_respondents <- 150
n_tasks <- 10
n_items_per_task <- 4

# 创建MaxDiff数据框
maxdiff_data <- data.frame()

cat("生成", n_respondents, "个受访者的数据，每人", n_tasks, "个任务...\n")

for (resp in 1:n_respondents) {
  for (task in 1:n_tasks) {
    # 随机选择4个属性
    selected_items <- sample(attributes, n_items_per_task)
    
    # 为每个受访者设置不同的偏好模式（模拟真实偏好）
    # 创建个体偏好向量
    if (resp <= 50) {
      # 第一组：偏好高端品牌
      pref_weights <- c(Apple = 0.3, Samsung = 0.25, Huawei = 0.15, 
                       Xiaomi = 0.1, OnePlus = 0.1, Google = 0.05, Sony = 0.03, LG = 0.02)
    } else if (resp <= 100) {
      # 第二组：偏好性价比品牌
      pref_weights <- c(Apple = 0.1, Samsung = 0.15, Huawei = 0.2, 
                       Xiaomi = 0.25, OnePlus = 0.2, Google = 0.05, Sony = 0.03, LG = 0.02)
    } else {
      # 第三组：平衡偏好
      pref_weights <- c(Apple = 0.15, Samsung = 0.18, Huawei = 0.17, 
                       Xiaomi = 0.15, OnePlus = 0.15, Google = 0.1, Sony = 0.05, LG = 0.05)
    }
    
    # 基于偏好权重选择最佳和最差
    item_probs <- pref_weights[selected_items]
    item_probs <- item_probs / sum(item_probs)
    
    # 选择最佳项目（基于偏好概率）
    best_item <- sample(selected_items, 1, prob = item_probs)
    
    # 选择最差项目（基于反向偏好概率）
    remaining_items <- selected_items[selected_items != best_item]
    worst_probs <- 1 - pref_weights[remaining_items]
    worst_probs <- worst_probs / sum(worst_probs)
    worst_item <- sample(remaining_items, 1, prob = worst_probs)
    
    # 创建任务数据
    task_data <- data.frame(
      respondent = resp,
      task = task,
      item = selected_items,
      best = ifelse(selected_items == best_item, 1, 0),
      worst = ifelse(selected_items == worst_item, 1, 0),
      stringsAsFactors = FALSE
    )
    
    maxdiff_data <- rbind(maxdiff_data, task_data)
  }
}

# 查看数据结构
cat("\n数据结构预览:\n")
print(head(maxdiff_data, 12))
cat("数据维度:", dim(maxdiff_data), "\n")

# 数据预处理 - 为choicemodel包准备数据
cat("\n=== 数据预处理 ===\n")

# 创建选择变量
maxdiff_data$choice <- ifelse(maxdiff_data$best == 1, 1, 
                             ifelse(maxdiff_data$worst == 1, -1, 0))

# 创建设计矩阵
design_matrix <- model.matrix(~ item - 1, data = maxdiff_data)
colnames(design_matrix) <- paste0("attr_", gsub("[^A-Za-z0-9]", "_", colnames(design_matrix)))

# 为choicemodel准备数据格式
choice_data <- data.frame(
  id = maxdiff_data$respondent,
  task = maxdiff_data$task,
  choice = maxdiff_data$choice,
  design_matrix
)

cat("设计矩阵维度:", dim(design_matrix), "\n")
cat("选择数据维度:", dim(choice_data), "\n")

# === 使用mlogit进行MaxDiff分析 ===
cat("\n=== 使用mlogit进行MaxDiff分析 ===\n")

# 准备数据用于mlogit包
library(mlogit)

# 创建mlogit格式的数据
mlogit_data <- maxdiff_data
mlogit_data$choice_var <- ifelse(mlogit_data$best == 1, "best",
                                ifelse(mlogit_data$worst == 1, "worst", "none"))

# 为mlogit重新格式化数据
mlogit_formatted <- mlogit.data(mlogit_data,
                               choice = "choice_var",
                               shape = "long",
                               id.var = "respondent",
                               alt.var = "item")

cat("mlogit数据格式化完成\n")

# === 贝叶斯分析 ===
cat("\n=== 贝叶斯MaxDiff分析 ===\n")

# 使用bayesm包进行贝叶斯分析
library(bayesm)

# 准备贝叶斯分析数据
bayesian_data <- list()
for (resp in 1:n_respondents) {
  resp_data <- maxdiff_data[maxdiff_data$respondent == resp, ]
  
  # 为每个受访者创建数据
  X_list <- list()
  y_list <- list()
  
  for (task in 1:n_tasks) {
    task_data <- resp_data[resp_data$task == task, ]
    
    # 创建设计矩阵
    X_task <- model.matrix(~ item - 1, data = task_data)
    y_task <- task_data$choice
    
    X_list[[task]] <- X_task
    y_list[[task]] <- y_task
  }
  
  bayesian_data[[resp]] <- list(X = X_list, y = y_list)
}

cat("准备贝叶斯分析数据完成\n")

# 使用MCMCpack进行贝叶斯logit回归
library(MCMCpack)

# 创建汇总数据用于贝叶斯分析
summary_data <- maxdiff_data %>%
  group_by(item) %>%
  summarise(
    best_count = sum(best),
    worst_count = sum(worst),
    total_appearances = n(),
    net_score = sum(choice),
    .groups = 'drop'
  )

cat("\n属性汇总统计:\n")
print(summary_data)

# 计算基础效用值
utilities <- summary_data$net_score
names(utilities) <- summary_data$item

# 标准化效用值
utilities_std <- (utilities - mean(utilities)) / sd(utilities)

cat("\n=== 分析结果 ===\n")
cat("原始效用值:\n")
print(sort(utilities, decreasing = TRUE))

cat("\n标准化效用值:\n")
print(sort(utilities_std, decreasing = TRUE))

# 计算相对重要性
shifted_utils <- utilities - min(utilities)
relative_importance <- shifted_utils / sum(shifted_utils) * 100

cat("\n相对重要性 (%):\n")
for (i in 1:length(relative_importance)) {
  cat(sprintf("%s: %.1f%%\n", names(relative_importance)[i], relative_importance[i]))
}

# 可视化结果
library(ggplot2)

# 创建结果数据框
results_df <- data.frame(
  attribute = names(utilities),
  utility = as.numeric(utilities),
  relative_importance = as.numeric(relative_importance[names(utilities)])
)

# 效用值图表
p1 <- ggplot(results_df, aes(x = reorder(attribute, utility), y = utility)) +
  geom_bar(stat = "identity", fill = "steelblue", alpha = 0.7) +
  coord_flip() +
  labs(title = "MaxDiff分析结果 - 属性效用值",
       subtitle = "使用choicemodel 1.3.1版本",
       x = "智能手机品牌",
       y = "效用值") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5),
        plot.subtitle = element_text(hjust = 0.5))

print(p1)

# 相对重要性图表
p2 <- ggplot(results_df, aes(x = reorder(attribute, relative_importance), y = relative_importance)) +
  geom_bar(stat = "identity", fill = "darkgreen", alpha = 0.7) +
  coord_flip() +
  labs(title = "MaxDiff分析结果 - 相对重要性",
       subtitle = "基于贝叶斯分析方法",
       x = "智能手机品牌",
       y = "相对重要性 (%)") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5),
        plot.subtitle = element_text(hjust = 0.5))

print(p2)

# 保存图表
ggsave("maxdiff_utilities.png", plot = p1, width = 10, height = 6, dpi = 300)
ggsave("maxdiff_importance.png", plot = p2, width = 10, height = 6, dpi = 300)

cat("\n=== 分析完成 ===\n")
cat("样本大小:", n_respondents, "个受访者\n")
cat("任务数量:", n_tasks, "个任务/受访者\n")
cat("总观察数:", nrow(maxdiff_data), "\n")
cat("图表已保存为 maxdiff_utilities.png 和 maxdiff_importance.png\n")
