# MaxDiff分析 - 合并版本
# 设置CRAN镜像
options(repos = c(CRAN = "https://cran.rstudio.com/"))

# 不需要额外的包，使用基础R进行分析

# 构造MaxDiff样例数据
set.seed(123)

# 创建属性列表（例如：品牌偏好研究）
attributes <- c("Apple", "Samsung", "Huawei", "Xiaomi", "OnePlus", "Google")

# 生成MaxDiff设计矩阵
n_respondents <- 100
n_tasks <- 8
n_items_per_task <- 4

# 创建MaxDiff数据框
maxdiff_data <- data.frame()

for (resp in 1:n_respondents) {
  for (task in 1:n_tasks) {
    # 随机选择4个属性
    selected_items <- sample(attributes, n_items_per_task)
    
    # 模拟最佳和最差选择
    best_item <- sample(selected_items, 1)
    worst_item <- sample(selected_items[selected_items != best_item], 1)
    
    # 创建任务数据
    task_data <- data.frame(
      respondent = resp,
      task = task,
      item = selected_items,
      best = ifelse(selected_items == best_item, 1, 0),
      worst = ifelse(selected_items == worst_item, 1, 0)
    )
    
    maxdiff_data <- rbind(maxdiff_data, task_data)
  }
}

# 查看数据结构
head(maxdiff_data, 12)
cat("数据维度:", dim(maxdiff_data), "\n")

# 使用基础R进行MaxDiff分析
# 创建选择矩阵
choice_data <- maxdiff_data
choice_data$choice <- ifelse(choice_data$best == 1, 1, 
                           ifelse(choice_data$worst == 1, -1, 0))

# 计算每个属性的得分
attribute_scores <- aggregate(choice_data$choice, 
                            by = list(choice_data$item), 
                            FUN = sum)
names(attribute_scores) <- c("attribute", "score")

# 计算效用值（标准化得分）
utilities <- attribute_scores$score
names(utilities) <- attribute_scores$attribute

print("属性得分:")
print(attribute_scores)
print("属性效用值:")
print(utilities)

# 排序显示结果
sorted_utilities <- sort(utilities, decreasing = TRUE)
cat("\n=== 品牌偏好排名 ===\n")
for (i in 1:length(sorted_utilities)) {
  cat(sprintf("%d. %s: 效用值 %d\n", i, names(sorted_utilities)[i], sorted_utilities[i]))
}

# === 分析摘要部分 ===
cat("\n=== MaxDiff分析完成 ===\n")
cat("样本大小:", n_respondents, "个受访者\n")
cat("任务数量:", n_tasks, "个任务/受访者\n")
cat("总观察数:", nrow(maxdiff_data), "\n")

# 计算相对重要性
if (exists("utilities")) {
  # 转换为正值并计算相对重要性
  shifted_utils <- utilities - min(utilities)
  relative_importance <- shifted_utils / sum(shifted_utils) * 100
  
  cat("\n相对重要性 (%):\n")
  for (i in 1:length(relative_importance)) {
    cat(sprintf("%s: %.1f%%\n", names(relative_importance)[i], relative_importance[i]))
  }
}

cat("\n=== 分析完成 ===\n")
