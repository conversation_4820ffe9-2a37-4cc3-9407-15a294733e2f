# 执行MaxDiff分析
source("maxdiff_analysis.R")

# 输出分析摘要
cat("\n=== MaxDiff分析完成 ===\n")
cat("样本大小:", n_respondents, "个受访者\n")
cat("任务数量:", n_tasks, "个任务/受访者\n")
cat("总观察数:", nrow(maxdiff_data), "\n")

# 计算相对重要性
if (exists("utilities")) {
  # 转换为正值并计算相对重要性
  shifted_utils <- utilities - min(utilities)
  relative_importance <- shifted_utils / sum(shifted_utils) * 100
  
  cat("\n相对重要性 (%):\n")
  for (i in 1:length(relative_importance)) {
    cat(sprintf("%s: %.1f%%\n", names(relative_importance)[i], relative_importance[i]))
  }
}